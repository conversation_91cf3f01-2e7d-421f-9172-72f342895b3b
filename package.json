{"name": "@discocil/fv-domain-library", "version": "1.13.0", "author": "Fourvenues", "license": "ISC", "description": "Fv Domain shared", "files": ["dist"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./application": {"import": "./dist/application/index.js", "require": "./dist/application/index.js", "types": "./dist/application/index.d.ts"}, "./domain": {"import": "./dist/domain/index.js", "require": "./dist/domain/index.js", "types": "./dist/domain/index.d.ts"}}, "scripts": {"build": "npm run check:depcruise && npm run clean && tsc -p tsconfig.build.json && tsc-alias -p tsconfig.json", "clean": "rm -rf dist", "check:depcruise": "npx depcruise src tests", "lint": "eslint \"{src,tests}/**/*.ts\"", "lint:fix": "eslint \"{src,tests}/**/*.ts\" --fix", "release": "semantic-release --no-ci", "tests": "IS_UNIT=true jest tests/unit --coverage=false --maxWorkers=50%"}, "lint-staged": {"{src,tests}/**/*.ts": ["npm run lint:fix", "npm run check:depcruise"]}, "dependencies": {"@dinero.js/currencies": "2.0.0-alpha.14", "@noble/hashes": "^1.8.0", "@paralleldrive/cuid2": "2.2.2", "@types/dinero.js": "1.9.4", "@types/luxon": "3.4.2", "bignumber.js": "9.1.2", "crypto-js": "^4.2.0", "dinero.js": "2.0.0-alpha.14", "libphonenumber-js": "1.12.6", "luxon": "3.5.0", "uuid": "11.1.0"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@discocil/eslint-config-fourvenues": "1.13.3", "@faker-js/faker": "9.6.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@semantic-release/gitlab": "13.2.4", "@semantic-release/release-notes-generator": "14.0.3", "@types/crypto-js": "^4.2.2", "@types/jest": "29.5.14", "@types/jest-json-schema": "6.1.4", "@types/node": "22.13.13", "dependency-cruiser": "16.10.0", "eslint": "9.23.0", "husky": "9.1.7", "jest": "29.7.0", "jest-json-schema": "6.1.0", "jest-mock-extended": "4.0.0-beta1", "lint-staged": "15.5.0", "semantic-release": "24.2.3", "ts-jest": "29.3.0", "ts-node": "10.9.2", "tsc-alias": "1.8.11", "typescript": "5.8.2"}}