# [1.13.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.7...v1.13.0) (2025-09-03)


### Bug Fixes

* convert to json + tests ([add013f](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/add013f4ca25bec1559353bdb4f231490d7a27ce))


### Features

* money and uuid object values now have a safe construction method for internal use.\n the generic primitive type now accepts the maybe. BREAKING CHANGE ([9927d67](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/9927d67ebdf0a6821946d5766b9ffb23356abf0a))

## [1.12.8](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.7...v1.12.8) (2025-09-03)


### Bug Fixes

* convert to json + tests ([add013f](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/add013f4ca25bec1559353bdb4f231490d7a27ce))

## [1.12.7](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.6...v1.12.7) (2025-08-12)


### Bug Fixes

* add new methods in object value object ([b91febb](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/b91febbd7fbd618afdf93a2f975fb2e6f3a3d3e5))

## [1.12.6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.5...v1.12.6) (2025-08-08)


### Bug Fixes

* add new payment provider ([e052b5b](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/e052b5bd928b687fe1543fba95b55b7d9936a73b))

## [1.12.5](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.4...v1.12.5) (2025-08-06)


### Bug Fixes

* add new methods in stamps value objects and toMinorUnit in money ([b052597](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/b052597a9b21980ca3dcb3872d95076b5fd68076))

## [1.12.4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.3...v1.12.4) (2025-08-04)


### Bug Fixes

* value objects ([3727a2c](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/****************************************))

## [1.12.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.2...v1.12.3) (2025-07-31)


### Bug Fixes

* get or else object value object ([80c7eed](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/80c7eedbbb77a51500d5ddd78043e2efaa0b89e8))

## [1.12.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.1...v1.12.2) (2025-07-31)


### Bug Fixes

* get or else object value object ([0f37edb](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/0f37edb7355096cb31c918489c497e1dd8fdd20a))

## [1.12.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.12.0...v1.12.1) (2025-07-31)


### Bug Fixes

* string value object random method is static ([ebf6739](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ebf6739ae5d20ecd709787614b5a89524f5c7a26))

# [1.12.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.5...v1.12.0) (2025-07-31)


### Features

* breaking change: remove guards "is null" and "is object" and moved on object value object. \n new method random in string. \n "convert to json" function to convert primitives in json ([e7a2acc](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/e7a2accb320650282a782c8bf6c0332a245f7fb7))

## [1.11.5](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.4...v1.11.5) (2025-07-24)


### Bug Fixes

* bad imports ([2bdd9da](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/2bdd9da65d188acf7e326f645909fefab609d7d9))

## [1.11.4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.3...v1.11.4) (2025-07-24)


### Bug Fixes

* add error contextualizer, new enums and uuid v4 creator ([ae4745b](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ae4745bda6ba019c368fc0d5a149536161db4315))
* bad imports ([4d2181d](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/4d2181dcbe6f54716e13e1605bb1106662332dcb))

## [1.11.4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.3...v1.11.4) (2025-07-24)


### Bug Fixes

* add error contextualizer, new enums and uuid v4 creator ([ae4745b](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ae4745bda6ba019c368fc0d5a149536161db4315))

## [1.11.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.2...v1.11.3) (2025-07-21)


### Bug Fixes

* add pending activation state in reservations enum ([7307726](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/730772654e58426ac36b0898f31738b5d224f1d2))

## [1.11.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.1...v1.11.2) (2025-07-18)


### Bug Fixes

* generate random unique entity id ([7395ea3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/7395ea3c7f9d485e84642f9ae704509f87bffd9c))

## [1.11.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.11.0...v1.11.1) (2025-07-16)


### Bug Fixes

* is number function in number value object ([27ca641](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/27ca641f358cc566d37790349fb11d0516b2ce9d))

# [1.11.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.10.0...v1.11.0) (2025-06-23)


### Bug Fixes

* imports ([f105cc5](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/f105cc5edfe2bac487402d18469dd32bd8c2f2c4))
* message fv error ([0017d66](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/0017d66c5f9d6606713b01d2ab0183fa13b42c8b))


### Features

* end method now returns duration in ms ([c1b2136](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/c1b213666f326cf7b78fe3ab9e35a45cb39ca409))

# [1.10.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.9.2...v1.10.0) (2025-06-16)


### Bug Fixes

* fv error now handles error inside error stack ([0633014](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/0633014964f07b555cde4e7ae0d59a207feef5ab))
* in first and last method in collection ([aaac303](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/aaac3033bb4eae902aca365f4dfa2c3058aba467))
* in fv error stack ([0a99473](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/0a99473a893e62644bc601fbf73d1c8432068214))
* in fv error stack ([3955f98](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/3955f989f7643743db59bda9d618a055f1a61702))
* in pricing value object ([74d2faf](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/74d2fafe9038dde5b9623638daabc72a9fbcb819))
* pr comments ([262b019](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/262b019682d51395ed7f9d3d9feabe1f6e021085))


### Features

* create Pricing class ([69b2f95](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/69b2f95fd10b246d90bae6ef34163ba85f18d21d))
* pr comments ([d21a158](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/d21a158c51ac7a62eef845170629989053147439))
* remove not needed export ([deb7dfd](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/deb7dfdad6fe38562a12745aacd930d2f7daafe4))

## [1.9.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.9.1...v1.9.2) (2025-06-12)


### Bug Fixes

* modify request in FvError for add generics types ([a01128a](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/a01128a9abcd223c7a264f1b4f4f6f069695a838))

## [1.9.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.9.0...v1.9.1) (2025-06-12)


### Bug Fixes

* add new method isMultipleOf in FvNumber ([4767607](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/4767607bcf974ee5e9f702538ff7eb4d4905a2a6))

# [1.9.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.8.1...v1.9.0) (2025-06-11)


### Bug Fixes

* pr comments ([356c4a1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/356c4a10492337d9fe6dc86f6dff5e59802b89c9))


### Features

* **money:** add applyPercentage and jsdoc ([fc18497](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/fc184974230c85e968fc6bd4e53f52e92fd174be))

## [1.8.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.8.0...v1.8.1) (2025-06-10)


### Bug Fixes

* errors ([99c2e8d](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/99c2e8d0f86d17f2486bac330c9050ad58fc77e5))
* errors ([0de6d93](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/0de6d9378eefb48c646e97bd0dd1dd50435c8892))

# [1.8.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.7.0...v1.8.0) (2025-06-10)


### Features

* modify exceptions builder ([ec2393e](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ec2393ef72a125b91dfeb139fe480f4b7c5855fd))

# [1.7.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.6.0...v1.7.0) (2025-06-09)


### Bug Fixes

* build ([83faeff](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/83faeffd5c6587f79a52f540f5bd2c47721d6ea7))
* revision ([ebd4485](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ebd448560f7b1bd906f5e61132fa422781dc85a9))


### Features

* fv error to string method ([dae52de](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/dae52de6827a3f0d9ac83ca4840d063250491d5a))

# [1.6.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.7...v1.6.0) (2025-06-09)


### Bug Fixes

* fvDate ([1b108c2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/1b108c2cb2d025329a416a39e9da6c82ad8ca532))
* imageFile ([84c63f5](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/84c63f53c30e3edbae5c2febe2cb8cc1e120478a))
* mime type ([4a445b0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/4a445b013e14b3c30d193ec4a2fe3e950ff696c4))
* pr comments ([f966bf4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/f966bf4b3522897bf392c374cd4d1657c004f198))
* pr comments ([abbc358](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/abbc35813e61579a0f4e2d1b492aa5daa8bf0c40))
* pr comments ([cce6035](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/cce60353c9984add303d4b98327c7fea0d6572f1))


### Features

* add fvError.autoContextualizable ([7813e28](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/7813e280be8bc40d568f2416478aed254e658009))
* change state name in ticket an reservation ([e32af9f](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/e32af9f24de569a8fc030ad9842964cd92077119))
* fvError add autoContextualizable property ([ffc84e0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ffc84e00ca7de890eeb3b193203f833feefe5dd6))
* implemented error stack ([edc0be6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/edc0be6dcd30a148b724a1ab30822a988c86b8dc))
* money error ([da9a800](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/da9a800e6712104c0b1e4defaf77a23b071d49bc))
* new generic type for array not empty ([aad9b5c](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/aad9b5cc15a3d267cf431adba8e0593f3b304543))

## [1.5.7](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.6...v1.5.7) (2025-05-21)

## [1.5.6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.5...v1.5.6) (2025-05-16)


### Bug Fixes

* new method in date value object and export index ([07b05bc](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/07b05bc20775a3b18e0a3e15da98b5fc31e75d13))

## [1.5.5](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.4...v1.5.5) (2025-05-08)


### Bug Fixes

* to seconds in date value object ([02d0009](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/02d0009f875eb05bf3a7631ac7e47e7aecad3535))

## [1.5.4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.3...v1.5.4) (2025-05-08)


### Bug Fixes

* in error base class ([ff801a7](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ff801a7cceac00b163bfdba104b70b7ecd9deddc))

## [1.5.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.2...v1.5.3) (2025-05-08)


### Bug Fixes

* new methods in date value object ([874ec5e](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/874ec5e0f896940bf186db81f0d64152d587fa17))

## [1.5.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.1...v1.5.2) (2025-05-07)


### Bug Fixes

* new enums ([75af034](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/75af034da175f12cb13019d8ecdba83767fd22f7))

## [1.5.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.1...v1.5.2) (2025-05-07)


### Bug Fixes

* new enums ([75af034](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/75af034da175f12cb13019d8ecdba83767fd22f7))

## [1.5.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.1...v1.5.2) (2025-05-07)


### Bug Fixes

* new enums ([75af034](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/75af034da175f12cb13019d8ecdba83767fd22f7))

## [1.5.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.5.0...v1.5.1) (2025-05-05)


### Bug Fixes

* new musical genres ([5875c55](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/5875c553a2c631bea65ab2951ff913aa54264fd7))

# [1.5.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.4.0...v1.5.0) (2025-04-29)


### Bug Fixes

* date format using timeZone ([42b54aa](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/42b54aaa391a1a0f70ff06cc4efd610ee40a39a8))
* **date:** remove default timezone in toFormat method ([17d1a62](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/****************************************))
* remove unused code ([31fe0ce](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/31fe0ce9fc9778930e8de73e219437215a4f2156))
* remove unused var ([be1d5b2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/be1d5b2911b9531a83913a5de22523c76ae1bcd9))


### Features

* **date:** add setTimezone method ([90eb6eb](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/90eb6eb929930e4a3333dd72b6adf3d6458addc8))

# [1.4.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.3...v1.4.0) (2025-04-29)


### Features

* add new method set time zome in date value object ([c0fe1e9](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/c0fe1e93558d186b35c5474e44169c89ce5b13d4))

## [1.3.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.2...v1.3.3) (2025-04-23)


### Bug Fixes

* replace crypto node library ([b85f4f9](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/b85f4f95885ba01d8834f7aab96f91c386efd8f5))
* replace crypto node library ([d96dc03](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/d96dc038bf43453a7d9ab99c4284599d35310d88))

## [1.3.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.2...v1.3.3) (2025-04-23)


### Bug Fixes

* replace crypto node library ([d96dc03](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/d96dc038bf43453a7d9ab99c4284599d35310d88))

## [1.3.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.1...v1.3.2) (2025-04-15)


### Bug Fixes

* in message error ([29f51b1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/29f51b1d8d53ee877e092ee93ce7a4b09aa8dfc1))

## [1.3.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.0...v1.3.1) (2025-04-14)


### Bug Fixes

* entity comparison in value object ([cbc6fda](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/cbc6fda2fb2e6e9cda72468dec14aa20355836fa))
* entity comparison in value object ([dd560a6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/dd560a60b23ab569d12ecfff19df645810b03838))

## [1.3.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.0...v1.3.1) (2025-04-14)


### Bug Fixes

* entity comparison in value object ([dd560a6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/dd560a60b23ab569d12ecfff19df645810b03838))

## [1.3.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.3.0...v1.3.1) (2025-04-14)


### Bug Fixes

* entity comparison in value object ([dd560a6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/dd560a60b23ab569d12ecfff19df645810b03838))

# [1.3.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.2.4...v1.3.0) (2025-04-14)


### Features

* new methods in value objects and services ([8042bdc](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/8042bdc66f26cb06c635716d96f4c0a3d3c7ee50))

## [1.2.4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.2.3...v1.2.4) (2025-04-08)


### Bug Fixes

* readme and methods names ([2933851](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/2933851cce800d052e673ac25bbafe66dd8a4e50))

## [1.2.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.2.2...v1.2.3) (2025-04-02)


### Bug Fixes

* export missing type in collection ([48b4a2b](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/48b4a2b097d48a2c45ec5ed3382e51a1f243fc63))
* export missing type in collection ([ab38a5b](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ab38a5bdec7404b0d7820b52ae1970f165aed715))

## [1.2.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.2.1...v1.2.2) (2025-04-02)


### Bug Fixes

* strengthen the typing of the collection ([7669b82](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/7669b82c863bf751db5b1481a50cfdc1ad0fe40b))

## [1.2.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.2.0...v1.2.1) (2025-04-01)


### Bug Fixes

* enums and collection performance ([354bbd3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/354bbd3adb58b4e1da28faa5e1de4149be7a1e30))

# [1.2.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.1.3...v1.2.0) (2025-03-31)


### Features

* add new method in date value object ([d7fae66](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/d7fae66f9973974959d4af111814ebe81f19b224))

## [1.1.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.1.2...v1.1.3) (2025-03-28)


### Bug Fixes

* bad types ([6003d13](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/6003d135eb90b4149fc568d80df9e9c50765d9f2))

## [1.1.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.1.1...v1.1.2) (2025-03-28)


### Bug Fixes

* missing methods and type fixes ([8ca1179](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/8ca11792bd6207899f2e01aa6e0c46f38787f7eb))

## [1.1.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.1.0...v1.1.1) (2025-03-27)


### Bug Fixes

* in generic type ([e1ea84f](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/e1ea84f54b592f81f1b63dcc1a5a27a9eb373ee8))

# [1.1.0](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.11...v1.1.0) (2025-03-27)


### Features

* new methods in timestamps value objects ([ce7e8af](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ce7e8af15b85a039166f682cfdce979d7ae077e7))

## [1.0.11](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.10...v1.0.11) (2025-03-27)


### Bug Fixes

* missing exports ([925e268](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/925e268931aafe632143b95f76812715ce19a1cd))

## [1.0.10](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.9...v1.0.10) (2025-03-27)


### Bug Fixes

* missing exports ([5bf1875](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/5bf1875d12f48511f25c6a066101d8f6aa60653f))

## [1.0.9](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.8...v1.0.9) (2025-03-26)


### Bug Fixes

* new value object Gender ([cb0e743](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/cb0e74361b1b0c8ba4ccdfec7ee1257bdc16c7c7))

## [1.0.8](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.7...v1.0.8) (2025-03-26)


### Bug Fixes

* readme ([b8f3df4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/b8f3df49afb91cbd5037de2e533a8b73579d1dc4))

## [1.0.7](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.6...v1.0.7) (2025-03-26)


### Bug Fixes

* export value object uuid ([54b7b85](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/54b7b85e5cc43b2b107879a37f5cd303c0012def))

## [1.0.6](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.5...v1.0.6) (2025-03-26)


### Bug Fixes

* export type missing ([ff72277](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/ff72277d9c55097f9c599a4999cdd8b9e033e04c))

## [1.0.5](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.4...v1.0.5) (2025-03-25)


### Bug Fixes

* improvements in imports and exports ([9a67c67](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/9a67c675cd6191164b8296c5b8e8db3a4512856d))

## [1.0.4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.3...v1.0.4) (2025-03-25)


### Bug Fixes

* readme notes ([bc5dfaf](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/bc5dfafc14297000c6df33e617d98edcbd6028b2))

## [1.0.3](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.2...v1.0.3) (2025-03-25)


### Bug Fixes

* percentage in money ([63ba30c](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/63ba30c847c48d383df596611307ffaf02241579))

## [1.0.2](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.1...v1.0.2) (2025-03-25)


### Bug Fixes

* tests configuration ([33bc92c](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/33bc92c2b1827047c78c373efb97dfeed7d829fc))

## [1.0.1](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/compare/v1.0.0...v1.0.1) (2025-03-25)


### Bug Fixes

* improvements in imports and exports ([141bfd4](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/141bfd48bcd40af5066ff7e184de2b7b20d02aae))

# 1.0.0 (2025-03-24)


### Features

* first commit ([7def7ca](https://gitlab.s.fourvenues.com/fourvenues/fv-domain-library/commit/7def7ca87b3b1e4a685875036365453bf253f9ba))
