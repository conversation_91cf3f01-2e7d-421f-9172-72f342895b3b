import {
  v4 as uuidv4,
  v7 as uuidv7,
  validate as uuidValidate,
  version as uuidVersion,
} from 'uuid';

import { left, right } from '../contracts/Result';
import { InvalidArgumentError } from '../errors/InvalidArgumentError';

import { Identifier } from './Identifier';

import type { Either } from '../contracts/Result';

export class UUID extends Identifier<string> {
  static pattern = '^[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$';

  private static readonly version = 7;

  private constructor(readonly value: string) {
    super(value);
  }

  private static validate(uuid: string): boolean {
    return uuidValidate(uuid);
  }

  private static validateVersion(uuid: string): boolean {
    try {
      const version = uuidVersion(uuid);

      return version === this.version;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return false;
    }
  }

  static create(): UUID {
    const uuid = uuidv7();

    return new this(uuid);
  }

  static createV4(): UUID {
    const uuid = uuidv4();

    return new this(uuid);
  }

  static isValid(uuid: string): boolean {
    return this.validate(uuid) && this.validateVersion(uuid);
  }

  static safeBuild(uuid: string): UUID {
    return new this(uuid);
  }

  static build(uuid: string): Either<InvalidArgumentError, UUID> {
    const isValid = this.isValid(uuid);

    if (isValid) {
      return right(UUID.safeBuild(uuid));
    }

    return left(InvalidArgumentError.build({
      context: 'UUID',
      data: { uuid },
    }));
  }
}
