import { FvString } from './String';
import { ValueObject } from './ValueObject';

import type { UnknownObject } from '../contracts/Generics';

export class FvObject extends ValueObject<UnknownObject> {
  static build(value: unknown): FvObject {
    const builderValue = this.is(value) ? value : {};

    return new this(builderValue);
  }

  static is(value: unknown): value is Record<string, unknown> {
    return typeof value === 'object';
  }

  static isNull(value: unknown): value is null {
    return value === null;
  }

  static isUndefined(value: unknown): value is undefined {
    return value === undefined;
  }

  /**
   * Indicates whether an object is null or undefined.
   */
  static isFalsy(value: unknown): value is null | undefined {
    return this.isNull(value) || this.isUndefined(value);
  }

  static getOrElse<RESPONSE = unknown>(
    target: UnknownObject | object | null | undefined,
    path: string,
    defaultValue: RESPONSE,
  ): RESPONSE {
    const responseDefault = defaultValue as RESPONSE;

    if (this.isFalsy(target) || !this.is(target) || !FvString.is(path)) {
      return responseDefault;
    }

    const keys = path.split('.');

    if (keys.length === 0) {
      return responseDefault;
    }

    let targetResult: unknown = target;

    for (const pathKey of keys) {
      if (!this.is(targetResult)) {
        return responseDefault;
      }

      targetResult = targetResult[pathKey];
    }

    return (targetResult as RESPONSE) ?? responseDefault;
  }

  toPrimitive(): UnknownObject {
    return this.value;
  }

  getOrElse<RESPONSE = unknown>(path: string, defaultValue: RESPONSE): RESPONSE {
    return FvObject.getOrElse(this.toPrimitive(), path, defaultValue);
  }

  isEmpty(): boolean {
    return Object.keys(this.value).length === 0;
  };

  removeUndefined(): FvObject {
    const newValue = Object.fromEntries(Object.entries(this.value).filter(([, v]) => v !== undefined));

    return new FvObject(newValue);
  };

  hasTruthyProperty(): boolean {
    return Object.values(this.value).some(value => Boolean(value));
  };
}
