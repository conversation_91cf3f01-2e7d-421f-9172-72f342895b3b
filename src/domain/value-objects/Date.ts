import { DateTime, Duration } from 'luxon';

import { DEFAULT_LENGUAGE } from '@/domain/config/Config';

import { left, right } from '../contracts/Result';
import { InvalidDateError } from '../errors/InvalidDateError';

import { ValueObject } from './ValueObject';

import type { DateTimeFormatOptions, DateTimeUnit } from 'luxon';
import type { Either } from '../contracts/Result';

type FormatStyle = 'date' | 'time' | 'timeShort' | 'full' | 'iso8601WithoutSeparator';

type FormatOption = Partial<Record<FormatStyle, DateTimeFormatOptions>>;

type SetOptions = {
  year?: number;
  month?: number;
  day?: number;
  hour?: number;
  minute?: number;
  second?: number;
  millisecond?: number;
};

type ToFormatOptions = {
  locale?: string;
  style?: FormatStyle;
  timeZone?: string;
};

export type DateEither = Either<InvalidDateError, FvDate>;

export enum EFormats {
  ISO8601_WITHOUT_SEPARATOR = 'yyyyMMdd',
  STANDARD = 'dd/MM/yyyy',
  TIME_SHORT = 'HH:mm'
}

export class FvDate extends ValueObject<Date> {
  private readonly defaultLenguage = DEFAULT_LENGUAGE;

  static readonly patterns: Record<string, string> = { ISO_8601: '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z$' };

  static readonly IN_MS = {
    ONE_SECOND: 1_000,
    FIVE_SECOND: 5_000,
    THIRTY_SECONDS: 30_000,
    ONE_MINUTE: 60_000,
    ONE_HOUR: 3_600_000,
    ONE_DAY: 86_400_000,
    ONE_WEEK: 604_800_000,
  };

  static readonly IN_SECONDS = {
    ONE_HOUR: 3_600,
    ONE_WEEK: 604_800,
    ONE_YEAR: 31_536_000,
    TEN: 10,
  };

  static readonly IN_HOURS = { TWO_DAYS: 48 };

  static readonly IN_MONTHS = { ONE_YEAR: 12 };

  static readonly IN_YEARS = { ONE_YEAR: 1 };

  private readonly formatOptions: FormatOption = {
    date: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    },
    full: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    },
    time: {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    },
    timeShort: {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    },
  };

  private _getValue(other: FvDate | Date): Date {
    return other instanceof FvDate ? other.value : other;
  }

  private static isDateValid(date: Date): boolean {
    return !isNaN(date.getTime());
  }

  private formatDate(locale: string, style: FormatOption[keyof FormatOption], date: Date): string {
    try {
      return new Intl.DateTimeFormat(locale, style).format(date);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return new Intl.DateTimeFormat(this.defaultLenguage, style).format(date);
    }
  }

  private endOf(unit: DateTimeUnit): FvDate {
    const date = DateTime.fromJSDate(this.value).endOf(unit);

    return new FvDate(date.toJSDate());
  }

  private startOf(unit: DateTimeUnit): FvDate {
    const date = DateTime.fromJSDate(this.value).startOf(unit);

    return new FvDate(date.toJSDate());
  }

  static create(value: Date = new Date()): FvDate {
    return new FvDate(value);
  }

  static createFromFormat(value: string, format: EFormats = EFormats.STANDARD): DateEither {
    const date = DateTime.fromFormat(value, format);
    const vo = new FvDate(date.toJSDate());

    if (!this.isDateValid(vo.value)) {
      return left(InvalidDateError.build({ context: 'FvDate.createFromFormat', data: { value, format } }));
    }

    return right(vo);
  }

  static createFromSeconds(value: number): FvDate {
    const date = DateTime.fromSeconds(value).toJSDate();

    return new FvDate(date);
  }

  static createFromMilliSeconds(value: number): FvDate {
    const date = new Date(value);

    return new FvDate(date);
  }

  static createFromISO(value: string): DateEither {
    const date = DateTime.fromISO(value);

    if (!date.isValid) {
      return left(InvalidDateError.build({ context: 'FvDate.createFromISO', data: { value } }));
    }

    return right(new FvDate(date.toJSDate()));
  }

  static createInitialEpoch(): FvDate {
    return new FvDate(new Date(0));
  }

  isEqualTo(other: FvDate | Date): boolean {
    const otherValue = this._getValue(other);

    return this.value.getTime() === otherValue.getTime();
  }

  isGreaterThan(other: FvDate | Date): boolean {
    const otherValue = this._getValue(other);

    return this.value.getTime() > otherValue.getTime();
  }

  isGreaterThanOrEqualTo(other: FvDate | Date): boolean {
    const otherValue = this._getValue(other);

    return this.value.getTime() >= otherValue.getTime();
  }

  isLessThan(other: FvDate | Date): boolean {
    const otherValue = this._getValue(other);

    return this.value.getTime() < otherValue.getTime();
  }

  isLessThanOrEqualTo(other: FvDate | Date): boolean {
    const otherValue = this._getValue(other);

    return this.value.getTime() <= otherValue.getTime();
  }

  toMilliseconds(): number {
    return DateTime.fromJSDate(this.value).toMillis();
  }

  toSeconds(): number {
    return DateTime.fromJSDate(this.value).toUnixInteger();
  }

  toPrimitive(): Date {
    return this.value;
  }

  getDiffInYears(date: Date): number {
    const startDate = DateTime.fromJSDate(this.value);
    const endDate = DateTime.fromJSDate(date);

    return endDate.diff(startDate, 'years').years;
  }

  addHours(hours: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.plus({ hours });

    return new FvDate(date.toJSDate());
  }

  subtractHours(hours: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.minus({ hours });

    return new FvDate(date.toJSDate());
  }

  addMinutes(minute: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.plus({ minute });

    return new FvDate(date.toJSDate());
  }

  subtractMinutes(minute: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.minus({ minute });

    return new FvDate(date.toJSDate());
  }

  addSeconds(second: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.plus({ second });

    return new FvDate(date.toJSDate());
  }

  subtractSeconds(second: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.minus({ second });

    return new FvDate(date.toJSDate());
  }

  isGreaterThanYear(date: Date): boolean {
    return this.getDiffInYears(date) > FvDate.IN_YEARS.ONE_YEAR;
  }

  isFuture(): boolean {
    const date = DateTime.fromJSDate(this.value);

    return date > DateTime.now();
  }

  isPast(): boolean {
    const date = DateTime.fromJSDate(this.value);

    return date < DateTime.now();
  }

  isBetween(startDate: Date | FvDate, endDate: Date | FvDate): boolean {
    if (startDate instanceof Date) {
      startDate = new FvDate(startDate);
    }

    if (endDate instanceof Date) {
      endDate = new FvDate(endDate);
    }

    return this.isGreaterThanOrEqualTo(startDate) && this.isLessThanOrEqualTo(endDate);
  }

  getHours(): number {
    return this.value.getHours();
  }

  getMinutes(): number {
    return this.value.getMinutes();
  }

  getSeconds(): number {
    return this.value.getSeconds();
  }

  equalTo(date: Date | FvDate): boolean {
    if (date instanceof FvDate) {
      return this.toMilliseconds() === date.toMilliseconds();
    }

    const newDateVo = DateTime.fromJSDate(date);

    return this.toMilliseconds() === newDateVo.toMillis();
  }

  toString(): string {
    const date = DateTime.fromJSDate(this.value);

    return date.toString();
  }

  toISODate(): string {
    const date = DateTime.fromJSDate(this.value);

    return `${date.toISODate()}`;
  }

  toISOTime(): string {
    const date = DateTime.fromJSDate(this.value);

    return `${date.toISOTime()}`;
  }

  toISO(): string {
    const date = DateTime.fromJSDate(this.value);

    return `${date.toISO()}`;
  }

  toRFC2822(): string {
    const date = DateTime.fromJSDate(this.value);

    return `${date.toRFC2822()}`;
  }

  toHTTP(): string {
    const date = DateTime.fromJSDate(this.value);

    return `${date.toHTTP()}`;
  }

  toFormat(options?: ToFormatOptions): string {
    const locale = options?.locale || this.defaultLenguage;
    const style = options?.style ?? 'full';
    const timeZone = options?.timeZone;

    if (style === 'iso8601WithoutSeparator') {
      const date = DateTime.fromJSDate(this.value);

      return date.toFormat('yyyyLLdd');
    }

    const matchStyle: FormatOption = {
      date: this.formatOptions.date,
      full: this.formatOptions.full,
      time: this.formatOptions.time,
      timeShort: this.formatOptions.timeShort,
    };

    const matchedStyle = matchStyle[style];

    if (matchedStyle && timeZone) {
      matchedStyle.timeZone = timeZone;
    }

    return this.formatDate(locale, matchedStyle, this.value);
  }

  addEpoch(otherDate: FvDate | Date): FvDate {
    if (otherDate instanceof Date) {
      otherDate = new FvDate(otherDate);
    }

    const newDate = this.toMilliseconds() + otherDate.toMilliseconds();

    return new FvDate(new Date(newDate));
  }

  subtractEpoch(date: FvDate): FvDate {
    const newDate = this.toMilliseconds() - date.toMilliseconds();

    return new FvDate(new Date(newDate));
  }

  set(dateComponents: SetOptions): FvDate {
    const date = DateTime.fromJSDate(this.value).set(dateComponents);

    return new FvDate(date.toJSDate());
  }

  subtractDays(days: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.minus({ days });

    return new FvDate(date.toJSDate());
  }

  subtractWeeks(weeks: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.minus({ weeks });

    return new FvDate(date.toJSDate());
  }

  subtractMonths(months: number): FvDate {
    let date = DateTime.fromJSDate(this.value);

    date = date.minus({ months });

    return new FvDate(date.toJSDate());
  }

  setTimezone(timezone: string): FvDate {
    const date = DateTime.fromJSDate(this.value);

    date.setZone(timezone);

    return new FvDate(date.toJSDate());
  }

  endOfMonth(): FvDate {
    return this.endOf('month');
  }

  endOfYear(): FvDate {
    return this.endOf('year');
  }

  endOfWeek(): FvDate {
    return this.endOf('week');
  }

  endOfDay(): FvDate {
    return this.endOf('day');
  }

  endOfHour(): FvDate {
    return this.endOf('hour');
  }

  startOfMonth(): FvDate {
    return this.startOf('month');
  }

  startOfYear(): FvDate {
    return this.startOf('year');
  }

  startOfWeek(): FvDate {
    return this.startOf('week');
  }

  startOfDay(): FvDate {
    return this.startOf('day');
  }

  startOfHour(): FvDate {
    return this.startOf('hour');
  }

  toISODuration(): string {
    const duration = Duration.fromMillis(this.toMilliseconds());

    return duration.toISO();
  }
}
