import { FvDate } from '../value-objects/Date';
import { UniqueEntityID } from '../value-objects/UniqueEntityID';

import type { Identifier } from '../value-objects/Identifier';

export interface DomainEventClass {
  EVENT_NAME: string;
}

type EventType = DomainEventClass['EVENT_NAME'];

type BaseDomainEventRequest<ATTRIBUTES = unknown> = {
  readonly type: EventType;
  readonly aggregateId: Identifier;
  readonly attributes: ATTRIBUTES;
};

type DomainEventPrimitives<ATTRIBUTES = unknown> = {
  readonly id: string;
  readonly type: EventType;
  readonly occurredOn: string;
  readonly aggregateId: string;
  readonly attributes: ATTRIBUTES;
};

export type DomainEventRequest<ATTRIBUTES = unknown> = Pick<BaseDomainEventRequest<ATTRIBUTES>, 'aggregateId' | 'attributes'>;

export abstract class DomainEvent<ATTRIBUTES = unknown> {
  static EVENT_NAME: EventType;

  readonly id: UniqueEntityID;
  readonly type: EventType;
  readonly occurredOn: FvDate;
  readonly aggregateId: Identifier;
  readonly attributes: ATTRIBUTES;

  protected constructor(params: BaseDomainEventRequest<ATTRIBUTES>) {
    const {
      aggregateId, type, attributes,
    } = params;

    this.type = type;
    this.aggregateId = aggregateId;
    this.attributes = attributes;
    this.id = UniqueEntityID.create();
    this.occurredOn = FvDate.create();
  }

  toPrimitives(): DomainEventPrimitives<ATTRIBUTES> {
    return {
      id: this.id.toPrimitive(),
      type: this.type,
      occurredOn: this.occurredOn.toISO(),
      aggregateId: this.aggregateId.toPrimitive(),
      attributes: this.attributes,
    };
  }
}
