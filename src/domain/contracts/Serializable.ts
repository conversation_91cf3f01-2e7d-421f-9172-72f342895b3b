import { Collection } from '../collections/Collection';
import { FvObject } from '../value-objects/Object';
import { FvString } from '../value-objects/String';

import { Maybe } from './Maybe';

import type { UnknownObject } from './Generics';

interface SerializableToPrimitives<T> {
  toPrimitives(): T;
}

interface SerializableToJson<T> {
  toJson(): ToJson<T>;
}

export interface Serializable<PRIMITIVES>
  extends SerializableToPrimitives<PRIMITIVES>,
  SerializableToJson<PRIMITIVES> {}

export const convertToJson = <T>(
  primitives: T,
): ToJson<T> => {
  const convert = (value: unknown): unknown => {
    if (value instanceof Maybe) {
      return value.fold(
        () => null,
        item => convert(item),
      );
    }

    if (value instanceof Set) {
      return Array.from(value).map(convert);
    }

    if (value instanceof Map) {
      return Array.from(value.entries()).map(([key, val]) => [
        convert(key),
        convert(val),
      ]);
    }

    if (value instanceof Collection) {
      return value.toArray().map(convert);
    }

    if (Array.isArray(value)) {
      return value.map(convert);
    }

    if (FvObject.isFalsy(value)) {
      return null;
    }

    if (value === '') {
      return value;
    }

    if (FvObject.is(value)) {
      const jsonResult: UnknownObject = {};

      for (const [k, v] of Object.entries(value)) {
        jsonResult[k] = convert(v);
      }

      return jsonResult;
    }

    const isEmptyString = (val: unknown): boolean =>
      FvString.is(val) && FvString.build(val).isEmpty();

    return isEmptyString(value) ? null : value;
  };

  return convert(primitives) as ToJson<T>;
};

// const jsonSerializer = <
//   PRIMITIVES extends object,
//   ENTITY extends SerializableToPrimitives<PRIMITIVES>,
// >(
//   entity: ENTITY,
// ) => {
//   return new Proxy(entity, {
//     get(target, prop) {
//       if (prop === 'toJson') {
//         return () => convertToJson(target);
//       }

//       return Reflect.get(target, prop);
//     },
//   }) as ENTITY & SerializableToJson<PRIMITIVES>;
// };

export type ToJson<T> =
  T extends Maybe<infer U>
    ? ToJson<U> | null
    : T extends Collection<infer V>
      ? Array<[ToJson<V>]>
      : T extends Map<infer K, infer V>
        ? Array<[ToJson<K>, ToJson<V>]>
        : T extends Set<infer U>
          ? ToJson<U>[]
          : T extends Array<infer U>
            ? ToJson<U>[]
            : T extends object
              ? { [K in keyof T]: ToJson<T[K]> }
              : T;
