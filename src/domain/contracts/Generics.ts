/* eslint-disable @typescript-eslint/no-unsafe-function-type */

import type { Money } from '../value-objects/Money';
import { ValueObject } from '../value-objects/ValueObject';
import type { Maybe } from './Maybe';

export type UnknownObject = Record<string, unknown>;

type Methods<T> = {
  [P in keyof T]: T[P] extends Function ? P : never;
}[keyof T];

type MethodsAndProperties<T> = {
  [P in keyof T]: T[P];
};

type PrimitiveValues = string | number | boolean | Date;

type ValueObjectValue<T> = {
  [P in keyof T]: T[P] extends Maybe<infer U>
    ? Maybe<ValueObjectValue<U>>
    : T[P] extends { value: unknown; }
      ? T[P] extends ValueObject<unknown>
      : Pick<T[P], 'value'>['value']
      ? T[P] extends Money
        ? T[P]['value']['amount']
        : Pick<T[P], 'value'>['value']
      : T[P] extends Array<{ value: unknown; }>
        ? Array<Pick<T[P][number], 'value'>['value']>
        : T[P] extends Function // Manejar funciones de manera especial
          ? T[P]
          : T[P] extends PrimitiveValues // Verificar si el tipo es una de las primitivas
            ? T[P] // Devolver el tipo primitivo
            : T[P] extends object[]
              ? Array<
                T[P][number] extends PrimitiveValues ? T[P][number] : T[P][number] extends object ? ValueObjectValue<T[P][number]> : unknown
              >
              : T[P] extends object
                ? ValueObjectValue<T[P]>
                : T[P];
};

type SnakeToCamelCase<T extends string> = T extends `${infer FirstPart}_${infer FirstLetter}${infer LastPart}`
  ? `${FirstPart}${Uppercase<FirstLetter>}${SnakeToCamelCase<LastPart>}`
  : T;

type CamelToSnakeCase<T extends string, P extends string = ''> = string extends T
  ? string
  : T extends `${infer C0}${infer R}`
    ? CamelToSnakeCase<R, `${P}${C0 extends Lowercase<C0> ? '' : '_'}${Lowercase<C0>}`>
    : P;

type FieldType<T> = T extends StringConstructor
  ? string
  : T extends NumberConstructor
    ? number
    : T extends BooleanConstructor
      ? boolean
      : T extends Array<infer U>
        ? U extends StringConstructor
          ? string[]
          : U extends NumberConstructor
            ? number[]
            : U extends BooleanConstructor
              ? boolean[]
              : U extends UnknownObject
                ? Array<TypeFromSchema<U>>
                : never
        : T extends Record<string, { type: unknown; }>
          ? TypeFromSchema<T>
          : never;

export type Properties<T> = Omit<MethodsAndProperties<T>, Methods<T>>;

export type Primitives<T> = ValueObjectValue<Properties<T>>;

export type SnakeToCamel<T> =
  T extends Array<infer U>
    ? Array<SnakeToCamel<U>>
    : T extends object
      ? {
        [P in keyof T as SnakeToCamelCase<string & P>]: T[P] extends Array<infer U>
          ? Array<SnakeToCamel<U>>
          : T[P] extends object
            ? SnakeToCamel<T[P]>
            : T[P];
      }
      : T;

export type CamelToSnake<T> =
  T extends Array<infer U>
    ? Array<CamelToSnake<U>>
    : T extends object
      ? {
        [P in keyof T as CamelToSnakeCase<string & P>]: T[P] extends Array<infer U> ? Array<CamelToSnake<U>> : CamelToSnake<T[P]>;
      }
      : T;

export type Mutable<T> = {
  -readonly [P in keyof T]: T[P];
};

export type Optional<T> = {
  [P in keyof T]?: T[P];
};

export type ReadonlyRecursive<T> = {
  readonly [P in keyof T]: ReadonlyRecursive<T[P]>;
};

export type Partial<T, K = keyof T> = MethodsAndProperties<
  {
    [P in keyof T as P extends K ? P : never]?: T[P];
  } & {
    [P in keyof T as P extends K ? never : P]: T[P];
  }
>;

export type Nullable<T> = {
  [P in keyof T]: T[P] | null | undefined;
};

type TypeFromSchema<T extends UnknownObject> = {
  [K in keyof T]: 'type' extends keyof T[K]
    ? FieldType<T[K]['type']> // Manejar propiedades con clave 'type'
    : T[K] extends UnknownObject
      ? TypeFromSchema<T[K]> // Manejar propiedades que son objetos directamente
      : never;
};

export type FlattenKeys<T, Prefix extends string = ''> = {
  [K in keyof T]: K extends string
    ? T[K] extends Array<unknown>
      ? `${Prefix}${K}`
      : T[K] extends object
        ? `${Prefix}${K}` | FlattenKeys<T[K], `${Prefix}${K}.`>
        : `${Prefix}${K}`
    : never;
}[keyof T];

export type NotEmptyArray<T> = [T, ...T[]];
